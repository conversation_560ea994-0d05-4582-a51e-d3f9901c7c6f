<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/transparent"
    android:fitsSystemWindows="false"
    android:fillViewport="true">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- <PERSON><PERSON><PERSON> de ajustes en la esquina superior derecha -->
        <Button
            android:id="@+id/settings_button"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="8dp"
            android:text="⚙️"
            android:textColor="#FFFFFF"
            android:textSize="20sp"
            android:background="@android:color/transparent"
            android:shadowColor="#000000"
            android:shadowDx="1"
            android:shadowDy="1"
            android:shadowRadius="2" />

        <!-- Sección 1: Velocímetro -->
        <LinearLayout
            android:id="@+id/speedometer_section"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/settings_button"
            android:layout_marginTop="32dp"
            android:orientation="vertical"
            android:gravity="center">

            <!-- Contenedor del velocímetro -->
            <RelativeLayout
                android:id="@+id/speedometer_container"
                android:layout_width="200dp"
                android:layout_height="120dp"
                android:layout_gravity="center">

                <!-- Vista del velocímetro personalizada -->
                <View
                    android:id="@+id/speedometer_background"
                    android:layout_width="200dp"
                    android:layout_height="120dp"
                    android:layout_centerInParent="true" />

                <!-- Aguja del velocímetro -->
                <View
                    android:id="@+id/speedometer_needle"
                    android:layout_width="2dp"
                    android:layout_height="80dp"
                    android:layout_centerHorizontal="true"
                    android:layout_alignParentBottom="true"
                    android:background="#FF0000"
                    android:transformPivotX="1dp"
                    android:transformPivotY="80dp" />

                <!-- Texto del porcentaje -->
                <TextView
                    android:id="@+id/speedometer_percentage"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                    android:layout_alignParentBottom="true"
                    android:layout_marginBottom="20dp"
                    android:text="50%"
                    android:textColor="#FFFFFF"
                    android:textSize="24sp"
                    android:textStyle="bold"
                    android:shadowColor="#FF0000"
                    android:shadowDx="0"
                    android:shadowDy="0"
                    android:shadowRadius="8" />
            </RelativeLayout>

            <!-- Botón Optimizar -->
            <Button
                android:id="@+id/optimize_button"
                android:layout_width="160dp"
                android:layout_height="50dp"
                android:layout_marginTop="24dp"
                android:text="OPTIMIZAR"
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                android:textStyle="bold"
                android:background="@android:color/transparent"
                android:shadowColor="#FF0000"
                android:shadowDx="0"
                android:shadowDy="0"
                android:shadowRadius="8" />
        </LinearLayout>

        <!-- Sección 2: Opciones avanzadas -->
        <LinearLayout
            android:id="@+id/advanced_options_section"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/speedometer_section"
            android:layout_marginTop="32dp"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Opción 1: Habilitar Opciones Avanzadas -->
            <LinearLayout
                android:id="@+id/option_developer_settings"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:padding="12dp"
                android:layout_marginBottom="8dp"
                android:clickable="true"
                android:focusable="true">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Habilitar Opciones Avanzadas"
                    android:textColor="#FFFFFF"
                    android:textSize="16sp"
                    android:shadowColor="#FF0000"
                    android:shadowDx="0"
                    android:shadowDy="0"
                    android:shadowRadius="4" />

                <ToggleButton
                    android:id="@+id/switch_developer_settings"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textOff="OFF"
                    android:textOn="ON"
                    android:textSize="12sp" />
            </LinearLayout>

            <!-- Opción 2: Acelerar GPU -->
            <LinearLayout
                android:id="@+id/option_gpu_acceleration"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:padding="12dp"
                android:layout_marginBottom="8dp"
                android:clickable="true"
                android:focusable="true">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Acelerar GPU"
                    android:textColor="#FFFFFF"
                    android:textSize="16sp"
                    android:shadowColor="#FF0000"
                    android:shadowDx="0"
                    android:shadowDy="0"
                    android:shadowRadius="4" />

                <ToggleButton
                    android:id="@+id/switch_gpu_acceleration"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:enabled="false"
                    android:textOff="OFF"
                    android:textOn="ON"
                    android:textSize="12sp" />
            </LinearLayout>

            <!-- Opción 3: Desactivar Animaciones -->
            <LinearLayout
                android:id="@+id/option_disable_animations"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:padding="12dp"
                android:layout_marginBottom="8dp"
                android:clickable="true"
                android:focusable="true">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Desactivar Animaciones"
                    android:textColor="#FFFFFF"
                    android:textSize="16sp"
                    android:shadowColor="#FF0000"
                    android:shadowDx="0"
                    android:shadowDy="0"
                    android:shadowRadius="4" />

                <ToggleButton
                    android:id="@+id/switch_disable_animations"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:enabled="false"
                    android:textOff="OFF"
                    android:textOn="ON"
                    android:textSize="12sp" />
            </LinearLayout>

            <!-- Opción 4: Limitar Apps en RAM -->
            <LinearLayout
                android:id="@+id/option_limit_background_apps"
                android:layout_width="match_parent"
                android:layout_height="60dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:padding="12dp"
                android:clickable="true"
                android:focusable="true">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="Limitar Apps en segundo plano"
                    android:textColor="#FFFFFF"
                    android:textSize="16sp"
                    android:shadowColor="#FF0000"
                    android:shadowDx="0"
                    android:shadowDy="0"
                    android:shadowRadius="4" />

                <ToggleButton
                    android:id="@+id/switch_limit_background_apps"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:enabled="false"
                    android:textOff="OFF"
                    android:textOn="ON"
                    android:textSize="12sp" />
            </LinearLayout>
        </LinearLayout>

        <!-- Sección 3: Apps favoritas -->
        <LinearLayout
            android:id="@+id/favorite_apps_section"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/advanced_options_section"
            android:layout_marginTop="32dp"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Agregar juegos o apps"
                android:textColor="#FFFFFF"
                android:textSize="18sp"
                android:textStyle="bold"
                android:layout_gravity="center"
                android:shadowColor="#FF0000"
                android:shadowDx="0"
                android:shadowDy="0"
                android:shadowRadius="4"
                android:layout_marginBottom="16dp" />

            <GridView
                android:id="@+id/favorite_apps_grid"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:numColumns="4"
                android:columnWidth="80dp"
                android:horizontalSpacing="8dp"
                android:verticalSpacing="8dp"
                android:stretchMode="columnWidth"
                android:gravity="center"
                android:listSelector="@android:color/transparent" />

            <!-- Botón agregar apps (se mostrará cuando no hay apps o al final de la lista) -->
            <ImageView
                android:id="@+id/add_apps_button"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_gravity="center"
                android:background="@android:color/transparent"
                android:src="@android:drawable/ic_input_add"
                android:scaleType="fitCenter"
                android:padding="16dp"
                android:clickable="true"
                android:focusable="true" />
        </LinearLayout>
    </RelativeLayout>
</ScrollView>