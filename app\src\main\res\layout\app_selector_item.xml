<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="80dp"
    android:layout_height="100dp"
    android:padding="4dp"
    android:background="@android:color/transparent">

    <ImageView
        android:id="@+id/app_icon"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:layout_centerHorizontal="true"
        android:scaleType="fitCenter"
        android:background="@android:color/transparent" />

    <TextView
        android:id="@+id/app_label"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/app_icon"
        android:layout_marginTop="2dp"
        android:text="App Name"
        android:textColor="#FFFFFF"
        android:textSize="10sp"
        android:gravity="center"
        android:maxLines="2"
        android:ellipsize="end"
        android:shadowColor="#000000"
        android:shadowDx="1"
        android:shadowDy="1"
        android:shadowRadius="2" />

    <!-- Indicador de selección -->
    <View
        android:id="@+id/selection_indicator"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:background="#FF0000"
        android:visibility="gone" />

</RelativeLayout>
